{"name": "sentry-ruby", "dockerComposeFile": "docker-compose.yml", "service": "sentry", "workspaceFolder": "/workspace/sentry", "features": {"ghcr.io/devcontainers/features/github-cli": {}, "ghcr.io/nils-geistmann/devcontainers-features/zsh": {}, "ghcr.io/devcontainers-extra/features/npm-packages": {}, "ghcr.io/rocker-org/devcontainer-features/apt-packages": {"packages": "inotify-tools nodejs npm chromium chromium-driver"}}, "customizations": {"vscode": {"extensions": ["sleistner.vscode-fileutils", "Shopify.ruby-lsp"], "settings": {}}, "rubyLsp.rubyVersionManager": {"identifier": "none"}}, "remoteUser": "sentry", "postCreateCommand": "ruby --version"}